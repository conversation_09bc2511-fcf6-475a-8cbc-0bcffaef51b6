Option Explicit

' 常量定义
Const WORK_START_HOUR As Integer = 9     ' 工作开始时间（小时）
Const WORK_END_HOUR As Integer = 17      ' 工作结束时间（小时）
Const MIN_ROWS_PER_DAY As Integer = 10   ' 每天最少行数
Const MAX_ROWS_PER_DAY As Integer = 15   ' 每天最多行数
Const MIN_TASK_SECONDS As Integer = 180  ' 最小任务处理时间（秒）
Const MAX_TASK_SECONDS As Integer = 300  ' 最大任务处理时间（秒）
Const MIN_INTERVAL_SECONDS As Integer = 30  ' 最小间隔时间（秒）
Const MAX_INTERVAL_SECONDS As Integer = 90  ' 最大间隔时间（秒）
Const MIN_END_BUFFER As Integer = 5      ' 工作结束前最小缓冲时间（分钟）
Const MAX_END_BUFFER As Integer = 30     ' 工作结束前最大缓冲时间（分钟）
Const TIME_FORMAT As String = "yyyy-mm-dd hh:mm:ss"  ' 时间格式

Sub 开始时间生成()
'
' 主程序 - 点击按钮后运行
' 让用户输入第一个报修时间，然后生成时间序列
'

Dim ws As Worksheet
Dim 第一个报修时间 As String
Dim 基准时间 As Date
Dim 用户输入 As String

' 设置工作表
Set ws = ActiveSheet

' 检查选择区域是否有效
If Selection.Columns.Count <> 2 Then
    MsgBox "请先选择两列数据（报修时间列和完成时间列）", vbExclamation
    Exit Sub
End If

' 显示时间格式说明并获取用户输入
用户输入 = InputBox("请输入第一个报修时间" & vbCrLf & vbCrLf & _
                   "支持的格式示例：" & vbCrLf & _
                   "? 2025-8-13 9:30:00" & vbCrLf & _
                   "? 2025/8/13 9:30:00" & vbCrLf & _
                   "? 2025-8-13 9:30" & vbCrLf & _
                   "? 8/13/2025 9:30" & vbCrLf & vbCrLf & _
                   "请输入:", "设置第一个报修时间", "2025-8-13 9:30:00")

' 如果用户取消，退出
If 用户输入 = "" Then
    Exit Sub
End If

' 验证并转换时间格式
If 验证时间格式(用户输入, 基准时间) Then
    ' 调用时间生成程序
    Call 生成时间序列(ws, 基准时间)
Else
    MsgBox "时间格式不正确！" & vbCrLf & _
           "请使用正确的日期时间格式，例如：" & vbCrLf & _
           "2025-8-13 9:30:00", vbExclamation
End If

End Sub

Function 验证时间格式(输入文本 As String, ByRef 输出时间 As Date) As Boolean
'
' 验证用户输入的时间格式是否正确
'
    On Error GoTo 格式错误
    
    ' 尝试转换为日期时间
    输出时间 = CDate(输入文本)
    
    ' 检查是否在合理范围内
    If Year(输出时间) < 2020 Or Year(输出时间) > 2030 Then
        GoTo 格式错误
    End If
    
    验证时间格式 = True
    Exit Function
    
格式错误:
    验证时间格式 = False
End Function

Sub 生成时间序列(ws As Worksheet, 基准时间 As Date)
'
' 根据基准时间生成完整的时间序列
'
    Dim selectionRange As Range
    Dim firstRow As Long, lastRow As Long
    Dim reportCol As Long, completeCol As Long
    Dim prevCompletionTime As Date
    Dim currentRowTime As Date, completionTime As Date
    Dim rowCounter As Integer, rowsPerDay As Integer
    Dim currentDate As Date, baseDate As Date
    Dim dayCounter As Integer
    Dim i As Long
    Dim needNewDay As Boolean
    Dim timeData() As Variant
    Dim oldScreenUpdating As Boolean
    Dim oldEnableEvents As Boolean

    ' 保存当前设置并优化性能
    oldScreenUpdating = Application.ScreenUpdating
    oldEnableEvents = Application.EnableEvents
    Application.ScreenUpdating = False
    Application.EnableEvents = False

    ' 初始化随机数生成器
    Randomize

    ' 获取选中区域
    Set selectionRange = Selection
    firstRow = selectionRange.Row
    lastRow = firstRow + selectionRange.Rows.Count - 1
    reportCol = selectionRange.Column       ' 第一列为报修时间列
    completeCol = reportCol + 1             ' 第二列为完成时间列

    ' 随机确定每天处理的行数（10-15行）
    rowsPerDay = Int((MAX_ROWS_PER_DAY - MIN_ROWS_PER_DAY + 1) * Rnd + MIN_ROWS_PER_DAY)

    ' 使用用户输入的基准时间
    baseDate = DateValue(基准时间)
    currentDate = baseDate

    ' 初始化数据数组
    ReDim timeData(1 To lastRow - firstRow + 1, 1 To 2)

    ' 初始化行计数器和日期计数器
    rowCounter = 0
    dayCounter = 0

    ' 处理每一行数据
    For i = 1 To lastRow - firstRow + 1
        ' 增加行计数器
        rowCounter = rowCounter + 1

        ' 检查是否需要增加日期（每rowsPerDay行增加一天）
        needNewDay = (rowCounter > 1 And ((rowCounter - 1) Mod rowsPerDay) = 0)

        If needNewDay Then
            dayCounter = dayCounter + 1
            currentDate = DateAdd("d", dayCounter, baseDate) '基于初始日期增加天数

            ' 新的一天使用随机的工作时间作为起点
            currentRowTime = currentDate + GetRandomWorkTime()
        ElseIf rowCounter = 1 Then
            ' 第一行使用用户输入的基准时间
            currentRowTime = 基准时间
        Else
            ' 同一天内的非第一行，在上一行完成时间基础上加随机间隔
            currentRowTime = DateAdd("s", GetRandomSeconds(MIN_INTERVAL_SECONDS, MAX_INTERVAL_SECONDS), prevCompletionTime)

            ' 如果时间超过工作结束时间，调整到下一天开始
            If Hour(currentRowTime) >= WORK_END_HOUR Then
                ' 获取下一天
                Dim nextDay As Date
                nextDay = GetNextDay(DateValue(currentRowTime))

                ' 使用下一天的随机开始时间
                currentRowTime = nextDay + GetRandomWorkTime()

                ' 如果日期变化，更新当前日期和日期计数器
                If DateValue(currentRowTime) <> currentDate Then
                    currentDate = DateValue(currentRowTime)
                    dayCounter = DateDiff("d", baseDate, currentDate)
                End If
            End If
        End If

        ' 生成完成时间（报修时间后的随机间隔）
        completionTime = DateAdd("s", GetRandomSeconds(MIN_TASK_SECONDS, MAX_TASK_SECONDS), currentRowTime)

        ' 如果完成时间超过工作结束时间，调整到当天工作结束时间前
        If Hour(completionTime) >= WORK_END_HOUR Then
            ' 调整到当天工作结束时间前的随机时间点
            completionTime = DateValue(currentRowTime) + TimeSerial(WORK_END_HOUR - 1, 60 - GetRandomSeconds(MIN_END_BUFFER, MAX_END_BUFFER), 0)
        End If

        ' 确保完成时间总是在报修时间之后
        If completionTime <= currentRowTime Then
            completionTime = DateAdd("s", GetRandomSeconds(MIN_TASK_SECONDS, MAX_TASK_SECONDS), currentRowTime)
        End If

        ' 存储时间到数组
        timeData(i, 1) = Format(currentRowTime, TIME_FORMAT)
        timeData(i, 2) = Format(completionTime, TIME_FORMAT)

        ' 更新上一行完成时间
        prevCompletionTime = completionTime
    Next i

    ' 一次性将数据写入工作表
    For i = 1 To UBound(timeData)
        Cells(firstRow + i - 1, reportCol).Value = timeData(i, 1)
        Cells(firstRow + i - 1, completeCol).Value = timeData(i, 2)
    Next i

    ' 恢复原始设置
    Application.ScreenUpdating = oldScreenUpdating
    Application.EnableEvents = oldEnableEvents

    Exit Sub

ErrorHandler:
    MsgBox "发生错误: " & Err.Description, vbCritical
    ' 恢复原始设置
    Application.ScreenUpdating = oldScreenUpdating
    Application.EnableEvents = oldEnableEvents
End Sub

' 生成随机工作时间
Private Function GetRandomWorkTime() As Date
    Dim randomHour As Integer
    Dim randomMinute As Integer

    randomHour = Int((WORK_END_HOUR - WORK_START_HOUR) * Rnd + WORK_START_HOUR)
    randomMinute = Int(60 * Rnd)
    GetRandomWorkTime = TimeSerial(randomHour, randomMinute, 0)
End Function

' 生成随机秒数
Private Function GetRandomSeconds(minSeconds As Integer, maxSeconds As Integer) As Integer
    GetRandomSeconds = Int((maxSeconds - minSeconds + 1) * Rnd + minSeconds)
End Function

' 获取下一天
Private Function GetNextDay(currentDate As Date) As Date
    GetNextDay = DateAdd("d", 1, currentDate)
End Function

Sub 创建时间生成按钮()
'
' 在E2位置创建开始按钮
'
    Dim ws As Worksheet
    Dim btn As Button
    
    Set ws = ActiveSheet
    
    ' 删除现有按钮（如果存在）
    On Error Resume Next
    ws.Buttons("时间生成按钮").Delete
    On Error GoTo 0
    
    ' 创建新按钮
    Set btn = ws.Buttons.Add(ws.Range("E2").Left, ws.Range("E2").Top, 100, 30)
    
    ' 设置按钮属性
    With btn
        .Name = "时间生成按钮"
        .Caption = "开始生成"
        .OnAction = "开始时间生成"
        .Font.Size = 12
        .Font.Bold = True
    End With
    
    MsgBox "时间生成按钮已添加到E2位置！" & vbCrLf & "点击按钮即可开始生成时间序列。"
End Sub


Private Sub CommandButton1_Click()

End Sub
