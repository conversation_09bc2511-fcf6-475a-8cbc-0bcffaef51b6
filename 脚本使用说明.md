# VBA 工单“时间自动生成”脚本使用说明（通俗版）

## 这段脚本能做什么
- 自动在表格里“报修时间（注意格式）”和“完成时间（注意格式）”两列填入时间
- 不用手动框选列，脚本会自动找这两列
- 只给“有内容的行”填时间（空行不处理）
- 第一条报修时间由你输入；后面的行会根据上一条时间，按规则自动往后排

---

## 适用环境
- WPS 表格 或 Microsoft Excel（需允许宏运行）
- 表头在第 1 行，且两列标题完全一致：
  - 报修时间（注意格式）
  - 完成时间（注意格式）
- 第 2 行开始是数据；A 列（报修用户）有内容的行视为“有效行”

---

## 快速开始（3 步）
1. 打开含有脚本的工作簿（或把 `123.vba` 中代码粘贴到当前文件的 VBA 编辑器）
2. 在数据表中，点击“开始生成”按钮（若没有按钮，见下文“没有按钮怎么办”）
3. 在弹出的输入框里，输入第一条报修时间（例：2025-08-13 09:30:00），确定后自动生成

输入格式示例：`2025-8-13 9:30:00`、`2025/8/13 9:30`、`8/13/2025 9:30`

---

## 工作原理（白话版）
- 脚本会在第 1 行找到两列：报修时间列 + 完成时间列
- 从第 2 行往下扫到最后一行：
  - 如果 A 列（报修用户）有内容，就当作“需要生成时间”的行
  - 第一条报修时间用你输入的时间
  - 后面每条报修时间 = 上一条完成时间 + 随机间隔（几十秒）
  - 每条完成时间 = 报修时间 + 随机处理时长（几分钟）
  - 如果时间超过下班点（默认 17:00），自动排到下一工作日 9:00–17:00 之间
- 生成完毕后，把两列一次性写回表格（速度快）

---

## 生成规则（关键点）
- 时间格式：`yyyy-mm-dd hh:mm:ss`
- 工作时间段：`09:00–17:00`（到点跨天）
- 单条处理时长：`3–5 分钟`（随机）
- 同一行之间的间隔：`30–90 秒`（随机）
- 只处理“有效行”（A 列非空）
- 会覆盖原来的时间值（如不想覆盖，见“常见问题”）

脚本里的关键参数在文件顶部常量里（如需微调，可在 VBA 中修改）：

```vb
Const WORK_START_HOUR As Integer = 9
Const WORK_END_HOUR   As Integer = 17
Const MIN_TASK_SECONDS As Integer = 180
Const MAX_TASK_SECONDS As Integer = 300
Const MIN_INTERVAL_SECONDS As Integer = 30
Const MAX_INTERVAL_SECONDS As Integer = 90
Const TIME_FORMAT As String = "yyyy-mm-dd hh:mm:ss"
```

---

## 没有按钮怎么办（手动放一个）
- 开启“开发工具” → “插入” → “按钮（窗体控件）”
- 贴在表里后，选择宏：`开始时间生成`
- 或直接运行：`Alt + F8` → 选“开始时间生成” → 运行

脚本也提供了自动加按钮的小工具（在 E2 放一个“开始生成”按钮）：宏名 `创建时间生成按钮`。

---

## 常见问题（FAQ）
- Q：提示“未找到目标列”？
  - A：确认表头文字完全匹配（含括号）：
    - `报修时间（注意格式）`
    - `完成时间（注意格式）`

- Q：哪些行会被处理？
  - A：从第 2 行开始，A 列（报修用户）非空的行。

- Q：会不会覆盖我原来手填的时间？
  - A：会覆盖。建议先备份；或把不想改动的行时间暂时清空/锁定。若需要“只填空白”的模式，可另行添加。

- Q：我不想用 9–17 的工作时间，能改吗？
  - A：可以，修改顶部常量 `WORK_START_HOUR/WORK_END_HOUR` 即可。

- Q：输入的第一条时间格式不对怎么办？
  - A：会弹出提示，请按示例格式重新输入。

---

## 自查清单（出问题先看这）
- 表头是否精确等于两个指定列名
- A 列是否为“报修用户”，且从第 2 行开始有值
- 宏是否被禁用（信任中心/宏安全设置）
- 第一条时间输入格式是否正确

---

## 版本说明（本脚本功能点）
- 自动查找两列（报修时间/完成时间）
- 只处理有效行（A 列非空）
- 第一条时间由用户输入，后续自动排布
- 遵循工作时段与随机间隔/时长规则

